<script lang="ts">
  import { onMount } from 'svelte';
  import { invoke } from '@tauri-apps/api/core';

  let result = $state<any>(null);
  let error = $state<string | null>(null);
  let loading = $state(false);

  async function testDictAPI() {
    loading = true;
    error = null;
    result = null;

    try {
      console.log('开始测试字典API...');
      const response = await invoke('sqlite_get_all_dicts');
      console.log('API响应:', response);
      result = response;
    } catch (err: any) {
      console.error('API调用失败:', err);
      error = err.message || '未知错误';
    } finally {
      loading = false;
    }
  }

  onMount(() => {
    testDictAPI();
  });
</script>

<div class="p-8">
  <h1 class="text-2xl font-bold mb-4">字典API测试</h1>
  
  <button 
    onclick={testDictAPI}
    class="bg-blue-500 text-white px-4 py-2 rounded mb-4"
    disabled={loading}
  >
    {loading ? '测试中...' : '重新测试'}
  </button>

  {#if loading}
    <div class="text-blue-600">加载中...</div>
  {/if}

  {#if error}
    <div class="text-red-600 bg-red-100 p-4 rounded mb-4">
      错误: {error}
    </div>
  {/if}

  {#if result}
    <div class="bg-gray-100 p-4 rounded">
      <h3 class="font-bold mb-2">API响应:</h3>
      <pre class="text-sm overflow-auto">{JSON.stringify(result, null, 2)}</pre>
    </div>
  {/if}
</div>
